import * as THREE from "three"

/**
 * 3D标注类型
 */
export const Annotation3DType = {
	POINT: "point",
	TEXT: "text",
	LINE: "line",
	POLYGON: "polygon",
} as const

export type Annotation3DTypeType = typeof Annotation3DType[keyof typeof Annotation3DType]

/**
 * 3D标注样式
 */
export interface Annotation3DStyle {
	// 文字样式
	fontSize?: number
	fontFamily?: string
	textColor?: string
	backgroundColor?: string
	borderColor?: string
	borderWidth?: number
	padding?: number

	// 点样式
	pointColor?: string
	pointSize?: number
	pointOpacity?: number

	// 线条样式
	lineColor?: string
	lineWidth?: number
	lineOpacity?: number
	lineDashArray?: number[]

	// 箭头样式
	arrowColor?: string
	arrowSize?: number

	// 可见性
	alwaysOnTop?: boolean
	fadeWithDistance?: boolean
	minDistance?: number
	maxDistance?: number
}

/**
 * 3D标注数据
 */
export interface Annotation3DData {
	id: string
	name: string
	type: Annotation3DType
	position: THREE.Vector3
	text?: string
	description?: string

	// 箭头和尺寸标注需要的额外点
	endPosition?: THREE.Vector3

	// 区域标注需要的多个点
	positions?: THREE.Vector3[]

	style: Annotation3DStyle
	visible: boolean
	locked: boolean

	// 元数据
	category?: string
	tags?: string[]
	properties?: Record<string, any>

	createdAt: Date
	updatedAt: Date
}

/**
 * 3D标注运行时数据
 */
export interface Annotation3DRuntime {
	data: Annotation3DData
	object3D: THREE.Object3D
	htmlElement?: HTMLElement
	isVisible: boolean
	distanceToCamera: number
}

/**
 * 默认样式
 */
export const DEFAULT_ANNOTATION_STYLES: Record<
	Annotation3DType,
	Annotation3DStyle
> = {
	[Annotation3DType.POINT]: {
		pointColor: "#ff4444",
		pointSize: 3, // 减小点的尺寸
		pointOpacity: 1.0,
		alwaysOnTop: true,
	},
	[Annotation3DType.TEXT]: {
		fontSize: 14,
		fontFamily: "Arial, sans-serif",
		textColor: "#333333",
		backgroundColor: "#ffffff",
		borderColor: "#cccccc",
		borderWidth: 1,
		padding: 8,
		alwaysOnTop: true,
		fadeWithDistance: true,
		minDistance: 10,
		maxDistance: 1000,
	},
	[Annotation3DType.LINE]: {
		lineColor: "#0066cc",
		lineWidth: 2,
		lineOpacity: 0.8,
		alwaysOnTop: false,
	},
	[Annotation3DType.POLYGON]: {
		lineColor: "#00aa00",
		lineWidth: 2,
		lineOpacity: 0.6,
		textColor: "#00aa00",
		fontSize: 12,
		backgroundColor: "rgba(255, 255, 255, 0.9)",
		alwaysOnTop: false,
	},
}

/**
 * 3D标注类别
 */
export interface Annotation3DCategory {
	id: string
	name: string
	icon: string
	color: string
	defaultStyle?: Partial<Annotation3DStyle>
}

/**
 * 预定义类别
 */
export const DEFAULT_ANNOTATION_CATEGORIES: Annotation3DCategory[] = [
	{
		id: "info",
		name: "信息标注",
		icon: "ℹ️",
		color: "#0066cc",
	},
	{
		id: "warning",
		name: "警告标注",
		icon: "⚠️",
		color: "#ff9900",
	},
	{
		id: "measurement",
		name: "测量标注",
		icon: "📏",
		color: "#666666",
	},
	{
		id: "landmark",
		name: "地标标注",
		icon: "🏛️",
		color: "#cc0066",
	},
	{
		id: "navigation",
		name: "导航标注",
		icon: "🧭",
		color: "#00cc66",
	},
]

/**
 * 编辑模式
 */
export const Annotation3DEditMode = {
	NONE: "none",
	ADD_POINT: "add_point",
	ADD_TEXT: "add_text",
	ADD_LINE: "add_line",
	ADD_POLYGON: "add_polygon",
	EDIT: "edit",
	MOVE: "move",
	DELETE: "delete",
} as const

export type Annotation3DEditModeType = typeof Annotation3DEditMode[keyof typeof Annotation3DEditMode]

/**
 * 编辑状态
 */
export interface Annotation3DEditState {
	mode: Annotation3DEditMode
	activeAnnotationId?: string
	tempPositions: THREE.Vector3[]
	isCreating: boolean
	isDragging: boolean
}

/**
 * 3D标注管理器接口
 */
export interface Annotation3DManager {
	// 基础操作
	addAnnotation(
		annotation: Omit<Annotation3DData, "id" | "createdAt" | "updatedAt">
	): string
	updateAnnotation(id: string, updates: Partial<Annotation3DData>): void
	deleteAnnotation(id: string): void
	getAnnotation(id: string): Annotation3DData | undefined
	getAllAnnotations(): Annotation3DData[]

	// 可见性控制
	setAnnotationVisibility(id: string, visible: boolean): void
	showAllAnnotations(): void
	hideAllAnnotations(): void

	// 分类管理
	getAnnotationsByCategory(category: string): Annotation3DData[]
	setAnnotationCategory(id: string, category: string): void

	// 编辑功能
	startEdit(mode: Annotation3DEditMode, annotationId?: string): void
	stopEdit(): void
	getEditState(): Annotation3DEditState

	// 3D交互
	updateCameraPosition(camera: THREE.Camera): void
	handleClick(intersectionPoint: THREE.Vector3, camera: THREE.Camera): void
	handleMouseMove(intersectionPoint: THREE.Vector3): void

	// 导入导出
	exportToJSON(): string
	importFromJSON(jsonData: string): void

	// 坐标转换
	setCoordinateConverter(converter: CoordinateConverter): void

	// 事件
	on(event: string, handler: (...args: any[]) => void): void
	off(event: string, handler: (...args: any[]) => void): void
	emit(event: string, ...args: any[]): void

	// 清理
	dispose(): void
}

/**
 * 工具函数：创建3D标注
 */
export function createAnnotation3D(
	name: string,
	type: Annotation3DType,
	position: THREE.Vector3,
	options: {
		text?: string
		description?: string
		endPosition?: THREE.Vector3
		positions?: THREE.Vector3[]
		style?: Partial<Annotation3DStyle>
		category?: string
		tags?: string[]
		properties?: Record<string, any>
	} = {}
): Omit<Annotation3DData, "id" | "createdAt" | "updatedAt"> {
	const defaultStyle = DEFAULT_ANNOTATION_STYLES[type]

	return {
		name,
		type,
		position: position.clone(),
		text: options.text,
		description: options.description,
		endPosition: options.endPosition?.clone(),
		positions: options.positions?.map((p) => p.clone()),
		style: { ...defaultStyle, ...options.style },
		visible: true,
		locked: false,
		category: options.category || "info",
		tags: options.tags || [],
		properties: options.properties || {},
	}
}

/**
 * 工具函数：计算两点间距离
 */
export function calculateDistance(
	point1: THREE.Vector3,
	point2: THREE.Vector3
): number {
	return point1.distanceTo(point2)
}

/**
 * 工具函数：计算多边形面积（3D投影到XZ平面）
 */
export function calculateArea(positions: THREE.Vector3[]): number {
	if (positions.length < 3) return 0

	let area = 0
	for (let i = 0; i < positions.length; i++) {
		const j = (i + 1) % positions.length
		area += positions[i].x * positions[j].z
		area -= positions[j].x * positions[i].z
	}
	return Math.abs(area) / 2
}

/**
 * 工具函数：格式化距离显示
 */
export function formatDistance(distance: number): string {
	if (distance < 1) {
		return `${(distance * 100).toFixed(1)} cm`
	} else if (distance < 1000) {
		return `${distance.toFixed(2)} m`
	} else {
		return `${(distance / 1000).toFixed(3)} km`
	}
}

/**
 * 工具函数：格式化面积显示
 */
export function formatArea(area: number): string {
	if (area < 1) {
		return `${(area * 10000).toFixed(1)} cm²`
	} else if (area < 10000) {
		return `${area.toFixed(2)} m²`
	} else {
		return `${(area / 10000).toFixed(3)} ha`
	}
}

/**
 * 地理坐标接口
 */
export interface GeoCoordinate {
	longitude: number // 经度
	latitude: number // 纬度
	altitude?: number // 海拔高度（可选）
}

/**
 * 带地理坐标的标注数据
 */
export interface Annotation3DGeoData
	extends Omit<Annotation3DData, "position" | "endPosition" | "positions"> {
	geoPosition: GeoCoordinate
	geoEndPosition?: GeoCoordinate
	geoPositions?: GeoCoordinate[]
}

/**
 * 坐标转换器接口
 */
export interface CoordinateConverter {
	// 世界坐标转地理坐标
	worldToGeo(worldPos: THREE.Vector3): GeoCoordinate
	// 地理坐标转世界坐标
	geoToWorld(geoPos: GeoCoordinate): THREE.Vector3
	// 获取地图中心点
	getMapCenter(): GeoCoordinate
}

/**
 * 工具函数：将标注数据转换为地理坐标格式
 */
export function annotationToGeoData(
	annotation: Annotation3DData,
	converter: CoordinateConverter
): Annotation3DGeoData {
	const geoData: Annotation3DGeoData = {
		...annotation,
		geoPosition: converter.worldToGeo(annotation.position),
	}

	if (annotation.endPosition) {
		geoData.geoEndPosition = converter.worldToGeo(annotation.endPosition)
	}

	if (annotation.positions) {
		geoData.geoPositions = annotation.positions.map((pos) =>
			converter.worldToGeo(pos)
		)
	}

	return geoData
}

/**
 * 工具函数：从地理坐标格式创建标注数据
 */
export function geoDataToAnnotation(
	geoData: Annotation3DGeoData,
	converter: CoordinateConverter
): Annotation3DData {
	const annotation: Annotation3DData = {
		...geoData,
		position: converter.geoToWorld(geoData.geoPosition),
		createdAt: geoData.createdAt || new Date(),
		updatedAt: geoData.updatedAt || new Date(),
	}

	if (geoData.geoEndPosition) {
		annotation.endPosition = converter.geoToWorld(geoData.geoEndPosition)
	}

	if (geoData.geoPositions) {
		annotation.positions = geoData.geoPositions.map((pos) =>
			converter.geoToWorld(pos)
		)
	}

	return annotation
}
