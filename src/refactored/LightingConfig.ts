import * as THREE from "three"

/**
 * 🌈 统一光照颜色规范配置系统
 * 控制天空盒、Mapbox地图、Three.js光照的所有颜色和强度参数
 */

// 时间段常量
export const TimeOfDay = {
    DAWN: 'dawn',       // 黎明 5:00-7:00
    MORNING: 'morning', // 上午 7:00-11:00
    NOON: 'noon',       // 正午 11:00-13:00
    AFTERNOON: 'afternoon', // 下午 13:00-17:00
    DUSK: 'dusk',       // 黄昏 17:00-19:00
    NIGHT: 'night'      // 夜晚 19:00-5:00
} as const

// 时间段类型
export type TimeOfDayType = typeof TimeOfDay[keyof typeof TimeOfDay]

// 完整的光照配置接口
export interface CompleteLightingConfig {
    // 🌌 天空盒配置
    sky: {
        sunIntensity: number
        fogColor: string
        horizonBlend: number
        fogRange: [number, number]
        highColor: string
        spaceColor: string
        starIntensity: number
    }
    
    // 🗺️ Mapbox地图配置
    mapbox: {
        brightness: number
        contrast: number
        saturation: number
        overlayColor: string
        overlayOpacity: number
    }
    
    // 💡 Three.js光照配置
    threejs: {
        // 直射光（太阳光）
        directionalLight: {
            color: string
            intensity: number
            position: THREE.Vector3
        }
        // 环境光
        ambientLight: {
            color: string
            intensity: number
        }
        // 半球光（天空光）
        hemisphereLight: {
            skyColor: string
            groundColor: string
            intensity: number
        }
    }
}

/**
 * 🎨 预定义的时间段光照配置
 */
export const LIGHTING_PRESETS: Record<TimeOfDayType, CompleteLightingConfig> = {
    // 🌅 黎明 (5:00-7:00)
    [TimeOfDay.DAWN]: {
        sky: {
            sunIntensity: 15,
            fogColor: "rgba(255, 180, 120, 0.8)", // 暖橙色
            horizonBlend: 0.25,
            fogRange: [0.8, 12],
            highColor: "rgba(255, 200, 150, 1)", // 暖黄色高空
            spaceColor: "rgba(100, 80, 120, 1)", // 紫色太空
            starIntensity: 0.2
        },
        mapbox: {
            brightness: 0.4, // 🌑 地图更暗
            contrast: 1.3,
            saturation: 0.8,
            overlayColor: "rgba(100, 50, 30, 0.4)", // 🌑 更深的暖色遮罩
            overlayOpacity: 0.4
        },
        threejs: {
            directionalLight: {
                color: "#FFB366", // 暖橙色阳光
                intensity: 4.5, // 💡 模型更亮
                position: new THREE.Vector3(0.5, 0.3, 0.8)
            },
            ambientLight: {
                color: "#FFE4B5", // 暖白色环境光
                intensity: 0.8 // 💡 环境光更亮
            },
            hemisphereLight: {
                skyColor: "#FFE4B5", // 暖天空色
                groundColor: "#8B7355", // 暖地面色
                intensity: 1.2 // 💡 半球光更亮
            }
        }
    },

    // ☀️ 上午 (7:00-11:00)
    [TimeOfDay.MORNING]: {
        sky: {
            sunIntensity: 25,
            fogColor: "rgba(200, 220, 255, 0.6)", // 清新蓝色
            horizonBlend: 0.15,
            fogRange: [1.2, 15],
            highColor: "rgba(135, 206, 250, 1)", // 天蓝色
            spaceColor: "rgba(100, 150, 255, 1)", // 明亮天空色
            starIntensity: 0
        },
        mapbox: {
            brightness: 1.0,
            contrast: 1.0,
            saturation: 1.0,
            overlayColor: "rgba(255, 255, 255, 0)", // 无遮罩
            overlayOpacity: 0
        },
        threejs: {
            directionalLight: {
                color: "#FFFFFF", // 纯白阳光
                intensity: 3.5,
                position: new THREE.Vector3(0.7, 0.7, 0.5)
            },
            ambientLight: {
                color: "#F0F8FF", // 淡蓝白色
                intensity: 0.6
            },
            hemisphereLight: {
                skyColor: "#87CEEB", // 天蓝色
                groundColor: "#DEB887", // 浅棕色地面
                intensity: 0.8
            }
        }
    },

    // 🌞 正午 (11:00-13:00)
    [TimeOfDay.NOON]: {
        sky: {
            sunIntensity: 35,
            fogColor: "rgba(180, 200, 255, 0.5)", // 清澈蓝色
            horizonBlend: 0.1,
            fogRange: [1.5, 20],
            highColor: "rgba(135, 206, 250, 1)", // 明亮天蓝
            spaceColor: "rgba(120, 180, 255, 1)", // 深天蓝
            starIntensity: 0
        },
        mapbox: {
            brightness: 1.1,
            contrast: 0.95,
            saturation: 0.9,
            overlayColor: "rgba(255, 255, 255, 0)", // 无遮罩
            overlayOpacity: 0
        },
        threejs: {
            directionalLight: {
                color: "#FFFFFF", // 强烈白光
                intensity: 4.0,
                position: new THREE.Vector3(0, 1, 0) // 正上方
            },
            ambientLight: {
                color: "#FFFFFF", // 纯白环境光
                intensity: 0.8
            },
            hemisphereLight: {
                skyColor: "#87CEEB", // 天蓝色
                groundColor: "#F5DEB3", // 浅黄地面
                intensity: 1.0
            }
        }
    },

    // 🌤️ 下午 (13:00-17:00)
    [TimeOfDay.AFTERNOON]: {
        sky: {
            sunIntensity: 30,
            fogColor: "rgba(200, 210, 240, 0.6)", // 柔和蓝色
            horizonBlend: 0.12,
            fogRange: [1.2, 18],
            highColor: "rgba(135, 206, 250, 1)", // 天蓝色
            spaceColor: "rgba(100, 150, 255, 1)", // 天空蓝
            starIntensity: 0
        },
        mapbox: {
            brightness: 0.95,
            contrast: 1.05,
            saturation: 1.1,
            overlayColor: "rgba(255, 240, 200, 0.05)", // 微暖遮罩
            overlayOpacity: 0.05
        },
        threejs: {
            directionalLight: {
                color: "#FFF8DC", // 微暖白光
                intensity: 3.2,
                position: new THREE.Vector3(-0.5, 0.6, 0.6)
            },
            ambientLight: {
                color: "#F5F5DC", // 米色环境光
                intensity: 0.7
            },
            hemisphereLight: {
                skyColor: "#87CEEB", // 天蓝色
                groundColor: "#DEB887", // 暖棕地面
                intensity: 0.9
            }
        }
    },

    // 🌅 黄昏 (17:00-19:00) - 优化天空渐变
    [TimeOfDay.DUSK]: {
        sky: {
            sunIntensity: 20,
            // 🌅 黄昏天空渐变：地面暖橙 → 高空深紫
            fogColor: "rgba(255, 180, 120, 0.7)", // 地面层：暖橙色
            horizonBlend: 0.4, // 增强地平线混合
            fogRange: [0.6, 12], // 适中的雾气范围
            highColor: "rgba(200, 100, 150, 1)", // 中空层：粉紫色
            spaceColor: "rgba(40, 20, 60, 1)", // 高空层：深紫色
            starIntensity: 0.3
        },
        mapbox: {
            brightness: 0.3, // 🌑 地图更暗
            contrast: 1.5,
            saturation: 0.8,
            overlayColor: "rgba(80, 40, 20, 0.5)", // 🌑 更深的橙色遮罩
            overlayOpacity: 0.5
        },
        threejs: {
            directionalLight: {
                color: "#FF8C00", // 橙色阳光
                intensity: 3.5, // 💡 黄昏阳光更亮
                position: new THREE.Vector3(-0.8, 0.2, 0.5)
            },
            ambientLight: {
                color: "#FFE4B5", // 暖环境光
                intensity: 0.6 // 💡 环境光更亮
            },
            hemisphereLight: {
                skyColor: "#FF6347", // 橙红天空
                groundColor: "#8B4513", // 深棕地面
                intensity: 1.0 // 💡 半球光更亮
            }
        }
    },

    // 🌙 夜晚 (19:00-5:00) - 修复天空渐变
    [TimeOfDay.NIGHT]: {
        sky: {
            sunIntensity: 5,
            // 🌌 夜晚天空渐变：地面浅蓝 → 高空深黑
            fogColor: "rgba(40, 50, 80, 0.8)", // 地面层：较浅的蓝色
            horizonBlend: 0.6, // 增强地平线混合，让渐变更明显
            fogRange: [0.3, 6], // 较近的雾气范围，让渐变更明显
            highColor: "rgba(20, 25, 45, 1)", // 中空层：中等深度蓝色
            spaceColor: "rgba(5, 5, 15, 1)", // 高空层：深黑色
            starIntensity: 1.0 // 最大星星强度
        },
        mapbox: {
            brightness: 0.15, // 🌑 夜晚地图非常暗
            contrast: 1.8,
            saturation: 0.5,
            overlayColor: "rgba(10, 15, 30, 0.7)", // 🌑 很深的蓝色遮罩
            overlayOpacity: 0.7
        },
        threejs: {
            directionalLight: {
                color: "#6495ED", // 💡 更亮的月光蓝色
                intensity: 2.0, // 💡 夜晚月光更亮
                position: new THREE.Vector3(-0.3, 0.8, -0.5)
            },
            ambientLight: {
                color: "#4169E1", // 💡 更亮的深蓝环境光
                intensity: 0.5 // 💡 夜晚环境光大幅增强
            },
            hemisphereLight: {
                skyColor: "#4169E1", // 💡 更亮的深蓝天空
                groundColor: "#2F4F4F", // 深灰地面
                intensity: 0.8 // 💡 夜晚半球光大幅增强
            }
        }
    }
}

/**
 * 🕐 根据时间获取对应的时间段
 */
export function getTimeOfDay(hour: number): TimeOfDayType {
    if (hour >= 5 && hour < 7) return TimeOfDay.DAWN
    if (hour >= 7 && hour < 11) return TimeOfDay.MORNING
    if (hour >= 11 && hour < 13) return TimeOfDay.NOON
    if (hour >= 13 && hour < 17) return TimeOfDay.AFTERNOON
    if (hour >= 17 && hour < 19) return TimeOfDay.DUSK
    return TimeOfDay.NIGHT
}

/**
 * 🎨 获取指定时间的光照配置
 */
export function getLightingConfig(time: Date): CompleteLightingConfig {
    const hour = time.getHours()
    const timeOfDay = getTimeOfDay(hour)
    return LIGHTING_PRESETS[timeOfDay]
}

/**
 * 🌈 在两个光照配置之间进行插值
 * 实现平滑的天气切换过渡效果
 */
export function interpolateLightingConfig(
    config1: CompleteLightingConfig,
    config2: CompleteLightingConfig,
    factor: number // 0-1之间
): CompleteLightingConfig {
    const lerp = (a: number, b: number, t: number) => a + (b - a) * t
    const lerpColor = (color1: string, color2: string, t: number): string => {
        // 🌈 改进的颜色插值 - 确保星星强度等数值正确插值
        if (t <= 0) return color1
        if (t >= 1) return color2

        // 对于rgba颜色，尝试解析并插值
        const rgba1Match = color1.match(/rgba?\(([^)]+)\)/)
        const rgba2Match = color2.match(/rgba?\(([^)]+)\)/)

        if (rgba1Match && rgba2Match) {
            const values1 = rgba1Match[1].split(',').map(v => parseFloat(v.trim()))
            const values2 = rgba2Match[1].split(',').map(v => parseFloat(v.trim()))

            if (values1.length >= 3 && values2.length >= 3) {
                const r = Math.round(lerp(values1[0], values2[0], t))
                const g = Math.round(lerp(values1[1], values2[1], t))
                const b = Math.round(lerp(values1[2], values2[2], t))
                const a = values1.length > 3 && values2.length > 3
                    ? lerp(values1[3], values2[3], t)
                    : (values1.length > 3 ? values1[3] : values2.length > 3 ? values2[3] : 1)

                return values1.length > 3 || values2.length > 3
                    ? `rgba(${r}, ${g}, ${b}, ${a})`
                    : `rgb(${r}, ${g}, ${b})`
            }
        }

        // 降级方案：简单切换
        return t < 0.5 ? color1 : color2
    }

    return {
        // 🌌 天空盒插值
        sky: {
            sunIntensity: lerp(config1.sky.sunIntensity, config2.sky.sunIntensity, factor),
            fogColor: lerpColor(config1.sky.fogColor, config2.sky.fogColor, factor),
            horizonBlend: lerp(config1.sky.horizonBlend, config2.sky.horizonBlend, factor),
            fogRange: [
                lerp(config1.sky.fogRange[0], config2.sky.fogRange[0], factor),
                lerp(config1.sky.fogRange[1], config2.sky.fogRange[1], factor)
            ] as [number, number],
            highColor: lerpColor(config1.sky.highColor, config2.sky.highColor, factor),
            spaceColor: lerpColor(config1.sky.spaceColor, config2.sky.spaceColor, factor),
            starIntensity: lerp(config1.sky.starIntensity, config2.sky.starIntensity, factor) // ⭐ 确保星星强度正确插值
        },

        // 🗺️ Mapbox地图插值
        mapbox: {
            brightness: lerp(config1.mapbox.brightness, config2.mapbox.brightness, factor),
            contrast: lerp(config1.mapbox.contrast, config2.mapbox.contrast, factor),
            saturation: lerp(config1.mapbox.saturation, config2.mapbox.saturation, factor),
            overlayColor: lerpColor(config1.mapbox.overlayColor, config2.mapbox.overlayColor, factor),
            overlayOpacity: lerp(config1.mapbox.overlayOpacity, config2.mapbox.overlayOpacity, factor)
        },

        // 💡 Three.js光照插值
        threejs: {
            directionalLight: {
                color: lerpColor(config1.threejs.directionalLight.color, config2.threejs.directionalLight.color, factor),
                intensity: lerp(config1.threejs.directionalLight.intensity, config2.threejs.directionalLight.intensity, factor),
                position: new THREE.Vector3().lerpVectors(config1.threejs.directionalLight.position, config2.threejs.directionalLight.position, factor)
            },
            ambientLight: {
                color: lerpColor(config1.threejs.ambientLight.color, config2.threejs.ambientLight.color, factor),
                intensity: lerp(config1.threejs.ambientLight.intensity, config2.threejs.ambientLight.intensity, factor)
            },
            hemisphereLight: {
                skyColor: lerpColor(config1.threejs.hemisphereLight.skyColor, config2.threejs.hemisphereLight.skyColor, factor),
                groundColor: lerpColor(config1.threejs.hemisphereLight.groundColor, config2.threejs.hemisphereLight.groundColor, factor),
                intensity: lerp(config1.threejs.hemisphereLight.intensity, config2.threejs.hemisphereLight.intensity, factor)
            }
        }
    }
}

/**
 * 🎬 光照过渡动画管理器
 */
export class LightingTransitionManager {
    private isTransitioning: boolean = false
    private transitionStartTime: number = 0
    private transitionDuration: number = 2000 // 2秒过渡时间
    private fromConfig: CompleteLightingConfig | null = null
    private toConfig: CompleteLightingConfig | null = null
    private onUpdate: ((config: CompleteLightingConfig) => void) | null = null
    private animationId: number | null = null

    /**
     * 🎬 开始光照过渡动画
     */
    public startTransition(
        from: CompleteLightingConfig,
        to: CompleteLightingConfig,
        duration: number = 2000,
        onUpdate: (config: CompleteLightingConfig) => void
    ): void {
        // 如果正在过渡，先停止
        this.stopTransition()

        this.fromConfig = from
        this.toConfig = to
        this.transitionDuration = duration
        this.onUpdate = onUpdate
        this.isTransitioning = true
        this.transitionStartTime = performance.now()

        console.log(`🎬 开始光照过渡动画 - 持续时间: ${duration}ms`)
        this.animate()
    }

    /**
     * 🎬 动画循环
     */
    private animate = (): void => {
        if (!this.isTransitioning || !this.fromConfig || !this.toConfig || !this.onUpdate) {
            return
        }

        const now = performance.now()
        const elapsed = now - this.transitionStartTime
        const progress = Math.min(elapsed / this.transitionDuration, 1)

        // 使用缓动函数让过渡更自然
        const easedProgress = this.easeInOutCubic(progress)

        // 插值计算当前配置
        const currentConfig = interpolateLightingConfig(this.fromConfig, this.toConfig, easedProgress)

        // 🐛 调试日志：检查星星强度插值
        console.log(`🎬 过渡进度: ${(progress * 100).toFixed(1)}%, 星星强度: ${this.fromConfig.sky.starIntensity} → ${this.toConfig.sky.starIntensity} = ${currentConfig.sky.starIntensity.toFixed(2)}`)

        // 更新光照
        this.onUpdate(currentConfig)

        if (progress >= 1) {
            // 过渡完成
            this.stopTransition()
            console.log('✅ 光照过渡动画完成')
        } else {
            // 继续下一帧
            this.animationId = requestAnimationFrame(this.animate)
        }
    }

    /**
     * 🛑 停止过渡动画
     */
    public stopTransition(): void {
        this.isTransitioning = false
        if (this.animationId) {
            cancelAnimationFrame(this.animationId)
            this.animationId = null
        }
    }

    /**
     * 📈 缓动函数 - 三次方缓入缓出
     */
    private easeInOutCubic(t: number): number {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
    }

    /**
     * 🔍 检查是否正在过渡
     */
    public isInTransition(): boolean {
        return this.isTransitioning
    }
}
